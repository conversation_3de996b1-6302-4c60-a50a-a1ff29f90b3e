/**
 * <PERSON><PERSON><PERSON> Component for Interviewer Dashboard
 * 
 * Simple pie chart placeholder component
 */

import React from 'react';
import { Card, Empty } from 'antd';

const PieChart = ({ data, title }) => {
  return (
    <Card className="pie-chart">
      <div className="text-center py-8">
        <Empty 
          description="Chart functionality will be implemented with a charting library"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
        {title && <div className="mt-2 text-gray-500">{title}</div>}
      </div>
    </Card>
  );
};

export default PieChart;
