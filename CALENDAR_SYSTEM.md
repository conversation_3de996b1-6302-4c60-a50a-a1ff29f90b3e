# Calendar System with Google Integration

## Overview

The Flyt platform now includes a comprehensive calendar system with Google Calendar sync and Google Meet integration. This system replaces the previous interview video call functionality and provides a more flexible scheduling solution for all user roles.

## Features

### ✅ Core Features
- **Multi-role Support**: Works for candidates, companies, and interviewers
- **Google Calendar Sync**: Two-way synchronization with Google Calendar
- **Google Meet Integration**: Create instant meetings or schedule them with events
- **Multiple Views**: Month, day, and agenda views
- **Event Management**: Full CRUD operations for calendar events
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Real-time Updates**: Events sync across all devices

### ✅ Google Integration
- **OAuth Authentication**: Secure Google account connection
- **Calendar Sync**: Automatic synchronization with Google Calendar
- **Meeting Creation**: Generate Google Meet links for online events
- **Participant Management**: Invite attendees to meetings
- **Conflict Detection**: Avoid scheduling conflicts

### ✅ Role-specific Features
- **Candidate**: Assessment scheduling, application deadlines, follow-ups
- **Company**: Candidate reviews, team meetings, hiring deadlines, onboarding
- **Interviewer**: Assessment reviews, preparation time, feedback sessions, training

## Architecture

### Components
```
src/
├── components/shared/Calendar.jsx          # Main calendar component
├── hooks/useCalendar.js                   # Calendar state management
├── services/
│   ├── calendar.service.js                # Google Calendar API integration
│   └── googleMeet.service.js              # Google Meet functionality
├── utils/googleApi.js                     # Google API utilities
└── pages/CalendarPage.jsx                 # Calendar page component
```

### Database Schema
```sql
calendar_events (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  date DATE NOT NULL,
  time TIME NOT NULL,
  duration INTEGER DEFAULT 60,
  type VARCHAR(50) DEFAULT 'meeting',
  location VARCHAR(255),
  is_online BOOLEAN DEFAULT false,
  meeting_link VARCHAR(500),
  participants TEXT[],
  google_event_id VARCHAR(255) UNIQUE,
  synced_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
```

## Setup Instructions

### 1. Google API Configuration

1. **Create Google Cloud Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one

2. **Enable APIs**
   - Enable Google Calendar API
   - Enable Google People API (for user info)

3. **Create OAuth 2.0 Credentials**
   - Go to Credentials section
   - Create OAuth 2.0 Client ID
   - Add your domain to authorized origins
   - Download the credentials

4. **Configure Environment Variables**
   ```env
   VITE_GOOGLE_CLIENT_ID=your_google_client_id
   VITE_GOOGLE_API_KEY=your_google_api_key
   ```

### 2. Database Setup

The calendar_events table is automatically created with proper RLS policies:

```sql
-- Table created with RLS enabled
-- Users can only access their own events
-- Automatic timestamp updates
-- Google Calendar sync support
```

### 3. Usage Examples

#### Basic Calendar Usage
```jsx
import Calendar from '@/components/shared/Calendar';

<Calendar
  userType="candidate"
  eventTypes={candidateEventTypes}
  participants={[]}
  viewOptions={{ month: true, day: true, agenda: true }}
/>
```

#### Using the Calendar Hook
```jsx
import useCalendar from '@/hooks/useCalendar';

const MyComponent = () => {
  const {
    events,
    loading,
    isGoogleConnected,
    connectGoogleCalendar,
    createEvent,
    syncWithGoogleCalendar
  } = useCalendar();

  // Component logic here
};
```

## API Reference

### useCalendar Hook

#### State
- `events`: Array of calendar events
- `loading`: Loading state for operations
- `syncing`: Google Calendar sync status
- `error`: Error messages
- `googleUser`: Connected Google user info
- `isGoogleConnected`: Google connection status
- `lastSyncTime`: Last sync timestamp

#### Actions
- `connectGoogleCalendar()`: Connect to Google Calendar
- `disconnectGoogleCalendar()`: Disconnect from Google Calendar
- `syncWithGoogleCalendar()`: Sync with Google Calendar
- `createEvent(eventData, options)`: Create new event
- `updateEvent(id, eventData, options)`: Update existing event
- `deleteEvent(id, options)`: Delete event
- `createInstantMeeting(meetingData)`: Create instant Google Meet

### Calendar Component Props

```jsx
Calendar.propTypes = {
  userType: PropTypes.oneOf(['candidate', 'company', 'interviewer']),
  eventTypes: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    color: PropTypes.string.isRequired
  })),
  participants: PropTypes.arrayOf(PropTypes.shape({
    name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired
  })),
  viewOptions: PropTypes.shape({
    month: PropTypes.bool,
    day: PropTypes.bool,
    agenda: PropTypes.bool
  })
};
```

## Security Considerations

### Row Level Security (RLS)
- Users can only access their own calendar events
- Google event IDs are unique to prevent conflicts
- Proper authentication required for all operations

### Google API Security
- OAuth 2.0 flow for secure authentication
- Scoped permissions for calendar access only
- Token refresh handling for long-term access

### Data Privacy
- Event data stored securely in Supabase
- Google sync is optional and user-controlled
- Participant data handled according to privacy policies

## Migration from Interview System

### Removed Components
- ❌ Interview video call system
- ❌ Interview creation forms
- ❌ Video call components
- ❌ Interview-specific database tables

### Preserved Components
- ✅ Interviewer role and dashboard
- ✅ Assessment functionality
- ✅ Interviewer profiles and settings
- ✅ Performance metrics and analytics

### Migration Steps
1. Interview-related data has been cleaned from stores
2. Calendar system provides scheduling functionality
3. Google Meet integration replaces video call system
4. All user roles retain their core functionality

## Testing

### Demo Component
Use the CalendarDemo component to test functionality:

```jsx
import CalendarDemo from '@/components/demo/CalendarDemo';

// Provides interactive demo with sample data
<CalendarDemo />
```

### Manual Testing
1. Connect Google Calendar
2. Create events with different types
3. Test sync functionality
4. Create Google Meet meetings
5. Test on different devices

## Troubleshooting

### Common Issues

1. **Google API not working**
   - Check environment variables
   - Verify API is enabled in Google Cloud Console
   - Check authorized origins

2. **Events not syncing**
   - Check Google Calendar connection
   - Verify permissions granted
   - Check network connectivity

3. **Database errors**
   - Verify RLS policies are correct
   - Check user authentication
   - Ensure proper table permissions

### Support
For issues or questions, refer to:
- Google Calendar API documentation
- Supabase documentation
- Component source code comments
