/**
 * CandidateTable Component for Interviewer Dashboard
 * 
 * Displays candidate information in a visually appealing card-based layout
 * with detailed profiles and interactive elements
 */

import React, { useState } from 'react';
import {
  Card,
  Avatar,
  Tag,
  Button,
  Row,
  Col,
  Typography,
  Space,
  Progress,
  Dropdown,
  Badge,
  Empty,
  Input,
  Divider,
  Tooltip,
  Modal,
  Tabs,
  List,
  Timeline,
  Skeleton,
} from 'antd';
import {
  UserOutlined,
  EyeOutlined,
  MessageOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  StarOutlined,
  StarFilled,
  MoreOutlined,
  SearchOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  MailOutlined,
  LinkedinOutlined,
  FileTextOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  BulbOutlined,
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const CandidateTable = ({ data = [], title = "Candidates", loading = false }) => {
  const [searchText, setSearchText] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('score');
  const [sortDirection, setSortDirection] = useState('desc');
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [favoriteIds, setFavoriteIds] = useState([]);

  // Filter and sort the data
  const filteredData = data
    .filter(candidate => {
      // Apply search filter
      const searchMatch = candidate.name.toLowerCase().includes(searchText.toLowerCase()) ||
        candidate.department?.toLowerCase().includes(searchText.toLowerCase()) ||
        candidate.email?.toLowerCase().includes(searchText.toLowerCase());
      
      // Apply status filter
      const statusMatch = filterStatus === 'all' || candidate.status === filterStatus;
      
      return searchMatch && statusMatch;
    })
    .sort((a, b) => {
      // Apply sorting
      if (sortBy === 'name') {
        return sortDirection === 'asc' 
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      } else if (sortBy === 'score') {
        return sortDirection === 'asc' 
          ? a.score - b.score
          : b.score - a.score;
      } else if (sortBy === 'date') {
        return sortDirection === 'asc' 
          ? new Date(a.date) - new Date(b.date)
          : new Date(b.date) - new Date(a.date);
      }
      return 0;
    });

  // Toggle favorite status
  const toggleFavorite = (id) => {
    setFavoriteIds(prev => 
      prev.includes(id) 
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };

  // View candidate details
  const viewCandidateDetails = (candidate) => {
    setSelectedCandidate(candidate);
    setDetailsVisible(true);
  };

  // Render candidate details modal
  const renderCandidateDetailsModal = () => {
    if (!selectedCandidate) return null;

    return (
      <Modal
        title={
          <div className="flex items-center justify-between">
            <span>Candidate Profile</span>
            <Button 
              type="text"
              icon={favoriteIds.includes(selectedCandidate.id) ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
              onClick={() => toggleFavorite(selectedCandidate.id)}
            />
          </div>
        }
        open={detailsVisible}
        onCancel={() => setDetailsVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setDetailsVisible(false)}>Close</Button>,
          <Button 
            key="schedule" 
            type="primary" 
            icon={<CalendarOutlined />}
          >
            Schedule Interview
          </Button>,
        ]}
      >
        <Row gutter={[24, 24]}>
          {/* Profile Header */}
          <Col span={24}>
            <div className="flex items-start">
              <Avatar 
                size={80} 
                src={selectedCandidate.avatar}
                icon={<UserOutlined />}
                style={{ backgroundColor: '#1890ff' }}
              />
              <div className="ml-4">
                <Title level={3} style={{ margin: 0 }}>{selectedCandidate.name}</Title>
                <Text type="secondary">{selectedCandidate.department || 'Software Developer'}</Text>
                <div className="mt-2 flex items-center">
                  <Tag color="blue">{selectedCandidate.status || 'Active'}</Tag>
                  <Tag color="green">{selectedCandidate.experience || '5+ years'}</Tag>
                  <Tag color="purple">{selectedCandidate.location || 'Remote'}</Tag>
                </div>
              </div>
            </div>
          </Col>

          {/* Tabs for different sections */}
          <Col span={24}>
            <Tabs defaultActiveKey="overview">
              <TabPane 
                tab={<span><FileTextOutlined /> Overview</span>}
                key="overview"
              >
                <Row gutter={[16, 24]}>
                  {/* Contact Information */}
                  <Col span={24}>
                    <Card title="Contact Information" size="small">
                      <Row gutter={16}>
                        <Col xs={24} md={8}>
                          <div className="flex items-center mb-3">
                            <MailOutlined className="mr-2 text-gray-500" />
                            <Text>{selectedCandidate.email || '<EMAIL>'}</Text>
                          </div>
                        </Col>
                        <Col xs={24} md={8}>
                          <div className="flex items-center mb-3">
                            <PhoneOutlined className="mr-2 text-gray-500" />
                            <Text>{selectedCandidate.phone || '+****************'}</Text>
                          </div>
                        </Col>
                        <Col xs={24} md={8}>
                          <div className="flex items-center mb-3">
                            <EnvironmentOutlined className="mr-2 text-gray-500" />
                            <Text>{selectedCandidate.location || 'New York, USA'}</Text>
                          </div>
                        </Col>
                      </Row>
                    </Card>
                  </Col>

                  {/* Skills Assessment */}
                  <Col xs={24} md={12}>
                    <Card title="Skills Assessment" size="small">
                      <div className="mb-3">
                        <div className="flex justify-between mb-1">
                          <Text>Technical Skills</Text>
                          <Text strong>{selectedCandidate.score || 85}%</Text>
                        </div>
                        <Progress 
                          percent={selectedCandidate.score || 85} 
                          status="active" 
                          strokeColor={{
                            '0%': '#108ee9',
                            '100%': '#87d068',
                          }}
                        />
                      </div>
                      <div className="mb-3">
                        <div className="flex justify-between mb-1">
                          <Text>Communication</Text>
                          <Text strong>{selectedCandidate.communication || 90}%</Text>
                        </div>
                        <Progress 
                          percent={selectedCandidate.communication || 90} 
                          status="active"
                          strokeColor={{
                            '0%': '#108ee9',
                            '100%': '#87d068',
                          }}
                        />
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <Text>Problem Solving</Text>
                          <Text strong>{selectedCandidate.problemSolving || 80}%</Text>
                        </div>
                        <Progress 
                          percent={selectedCandidate.problemSolving || 80} 
                          status="active"
                          strokeColor={{
                            '0%': '#108ee9',
                            '100%': '#87d068',
                          }}
                        />
                      </div>
                    </Card>
                  </Col>

                  {/* Interview History */}
                  <Col xs={24} md={12}>
                    <Card title="Interview History" size="small">
                      <Timeline>
                        <Timeline.Item color="green">
                          <Text strong>Technical Interview</Text>
                          <div>
                            <Text type="secondary">Completed on {selectedCandidate.lastInterview || '15 May 2025'}</Text>
                          </div>
                          <div>
                            <Text>Score: </Text>
                            <Tag color="green">{selectedCandidate.score || 85}%</Tag>
                          </div>
                        </Timeline.Item>
                        <Timeline.Item color="blue">
                          <Text strong>Initial Screening</Text>
                          <div>
                            <Text type="secondary">Completed on {selectedCandidate.screening || '10 May 2025'}</Text>
                          </div>
                          <div>
                            <Text>Status: </Text>
                            <Tag color="blue">Passed</Tag>
                          </div>
                        </Timeline.Item>
                        <Timeline.Item color="gray">
                          <Text strong>Application Received</Text>
                          <div>
                            <Text type="secondary">Received on {selectedCandidate.applied || '5 May 2025'}</Text>
                          </div>
                        </Timeline.Item>
                      </Timeline>
                    </Card>
                  </Col>

                  {/* Technical Skills */}
                  <Col span={24}>
                    <Card title="Technical Skills" size="small">
                      <div className="flex flex-wrap gap-2">
                        {(selectedCandidate.skills || ['JavaScript', 'React', 'Node.js', 'TypeScript', 'MongoDB', 'Express', 'CSS', 'HTML', 'Git']).map(skill => (
                          <Tag key={skill} color="blue">{skill}</Tag>
                        ))}
                      </div>
                    </Card>
                  </Col>

                  {/* Notes */}
                  <Col span={24}>
                    <Card title="Interview Notes" size="small">
                      <Paragraph>
                        {selectedCandidate.notes || 
                          'Candidate demonstrated strong problem-solving skills and technical knowledge. Communication was clear and professional. Would be a good fit for the team.'}
                      </Paragraph>
                    </Card>
                  </Col>
                </Row>
              </TabPane>

              <TabPane 
                tab={<span><TeamOutlined /> Experience</span>}
                key="experience"
              >
                <List
                  itemLayout="horizontal"
                  dataSource={selectedCandidate.experience || [
                    {
                      id: 1,
                      company: 'Tech Solutions Inc.',
                      position: 'Senior Developer',
                      duration: 'Jan 2022 - Present',
                      description: 'Led a team of 5 developers in building enterprise applications.'
                    },
                    {
                      id: 2,
                      company: 'Digital Innovations',
                      position: 'Software Developer',
                      duration: 'Mar 2019 - Dec 2021',
                      description: 'Developed and maintained web applications using React and Node.js.'
                    },
                    {
                      id: 3,
                      company: 'StartUp Labs',
                      position: 'Junior Developer',
                      duration: 'Jun 2017 - Feb 2019',
                      description: 'Assisted in the development of mobile and web applications.'
                    }
                  ]}
                  renderItem={item => (
                    <List.Item>
                      <List.Item.Meta
                        title={
                          <div className="flex justify-between">
                            <Text strong>{item.position}</Text>
                            <Text type="secondary">{item.duration}</Text>
                          </div>
                        }
                        description={
                          <>
                            <Text>{item.company}</Text>
                            <Paragraph className="mt-1">{item.description}</Paragraph>
                          </>
                        }
                      />
                    </List.Item>
                  )}
                />
              </TabPane>

              <TabPane 
                tab={<span><TrophyOutlined /> Achievements</span>}
                key="achievements"
              >
                <List
                  itemLayout="horizontal"
                  dataSource={selectedCandidate.achievements || [
                    {
                      id: 1,
                      title: 'Increased application performance by 40%',
                      description: 'Optimized database queries and implemented caching strategies.'
                    },
                    {
                      id: 2,
                      title: 'Led successful migration to microservices architecture',
                      description: 'Resulted in improved scalability and reduced deployment times.'
                    },
                    {
                      id: 3,
                      title: 'Reduced bug count by 60% through improved testing practices',
                      description: 'Implemented comprehensive unit and integration testing.'
                    }
                  ]}
                  renderItem={item => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={<BulbOutlined style={{ fontSize: '24px', color: '#faad14' }} />}
                        title={<Text strong>{item.title}</Text>}
                        description={<Paragraph>{item.description}</Paragraph>}
                      />
                    </List.Item>
                  )}
                />
              </TabPane>
            </Tabs>
          </Col>
        </Row>
      </Modal>
    );
  };

  // Render the candidate cards
  const renderCandidateCards = () => {
    if (loading) {
      return (
        <Row gutter={[16, 16]}>
          {[1, 2, 3, 4, 5, 6].map(i => (
            <Col xs={24} sm={12} md={8} lg={8} xl={6} key={i}>
              <Card>
                <Skeleton active avatar paragraph={{ rows: 3 }} />
              </Card>
            </Col>
          ))}
        </Row>
      );
    }

    if (filteredData.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <span>
              No candidates found
              {searchText && ` matching "${searchText}"`}
              {filterStatus !== 'all' && ` with status "${filterStatus}"`}
            </span>
          }
        />
      );
    }

    return (
      <Row gutter={[16, 16]}>
        {filteredData.map(candidate => {
          const isFavorite = favoriteIds.includes(candidate.id);
          return (
            <Col xs={24} sm={12} md={8} lg={8} xl={6} key={candidate.id}>
              <Badge.Ribbon text={candidate.status} color={candidate.status === 'Permanent' ? 'blue' : 'purple'} style={{ display: candidate.status ? 'block' : 'none' }}>
                <Card
                  hoverable
                  className="candidate-card"
                  actions={[
                    <Tooltip title="View Details">
                      <Button 
                        type="text" 
                        icon={<EyeOutlined />} 
                        onClick={() => viewCandidateDetails(candidate)}
                      />
                    </Tooltip>,
                    <Tooltip title="Schedule Interview">
                      <Button 
                        type="text" 
                        icon={<CalendarOutlined />} 
                      />
                    </Tooltip>,
                    <Tooltip title="Send Message">
                      <Button 
                        type="text" 
                        icon={<MessageOutlined />} 
                      />
                    </Tooltip>,
                    <Tooltip title={isFavorite ? "Remove from Favorites" : "Add to Favorites"}>
                      <Button 
                        type="text" 
                        icon={isFavorite ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />} 
                        onClick={() => toggleFavorite(candidate.id)}
                      />
                    </Tooltip>,
                  ]}
                >
                  <div className="flex items-center mb-4">
                    <Avatar 
                      size={64} 
                      src={candidate.avatar} 
                      icon={<UserOutlined />}
                      style={{ backgroundColor: '#1890ff' }}
                    />
                    <div className="ml-3">
                      <Text strong style={{ fontSize: '16px' }}>{candidate.name}</Text>
                      <div>
                        <Text type="secondary">{candidate.department || 'Software Developer'}</Text>
                      </div>
                      <div className="mt-1">
                        <Tag color="blue">{candidate.experience || '5+ years'}</Tag>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mb-3">
                    <Text type="secondary">Technical Score</Text>
                    <div className="flex items-center">
                      <Progress 
                        percent={candidate.score} 
                        size="small" 
                        status="active"
                        strokeColor={{
                          '0%': '#108ee9',
                          '100%': '#87d068',
                        }}
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <ClockCircleOutlined className="mr-1 text-gray-500" />
                      <Text type="secondary">{candidate.date || 'May 15, 2025'}</Text>
                    </div>
                    <Tag color={candidate.score >= 80 ? 'green' : candidate.score >= 60 ? 'orange' : 'red'}>
                      {candidate.score}%
                    </Tag>
                  </div>
                </Card>
              </Badge.Ribbon>
            </Col>
          );
        })}
      </Row>
    );
  };

  return (
    <div className="candidate-table-container">
      <Card
        title={
          <div className="flex items-center justify-between">
            <Title level={4} style={{ margin: 0 }}>{title}</Title>
            <div className="flex items-center">
              <Badge count={filteredData.length} showZero>
                <Text type="secondary">Candidates</Text>
              </Badge>
            </div>
          </div>
        }
        extra={
          <Space size="middle">
            <Input
              placeholder="Search candidates"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              style={{ width: 200 }}
              allowClear
            />
            <Dropdown
              menu={{
                items: [
                  { key: 'all', label: 'All Statuses' },
                  { key: 'Permanent', label: 'Permanent' },
                  { key: 'Contract', label: 'Contract' },
                ],
                onClick: ({ key }) => setFilterStatus(key),
              }}
              trigger={['click']}
            >
              <Button icon={<FilterOutlined />}>
                {filterStatus === 'all' ? 'All Statuses' : filterStatus}
              </Button>
            </Dropdown>
            <Dropdown
              menu={{
                items: [
                  { key: 'score-desc', label: 'Score (High to Low)' },
                  { key: 'score-asc', label: 'Score (Low to High)' },
                  { key: 'name-asc', label: 'Name (A-Z)' },
                  { key: 'name-desc', label: 'Name (Z-A)' },
                  { key: 'date-desc', label: 'Date (Newest)' },
                  { key: 'date-asc', label: 'Date (Oldest)' },
                ],
                onClick: ({ key }) => {
                  const [field, direction] = key.split('-');
                  setSortBy(field);
                  setSortDirection(direction);
                },
              }}
              trigger={['click']}
            >
              <Button icon={<SortAscendingOutlined />}>Sort</Button>
            </Dropdown>
          </Space>
        }
      >
        {renderCandidateCards()}
      </Card>

      {/* Candidate Details Modal */}
      {renderCandidateDetailsModal()}
    </div>
  );
};

export default CandidateTable;
