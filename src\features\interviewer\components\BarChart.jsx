/**
 * Bar<PERSON>hart Component for Interviewer Dashboard
 * 
 * Simple bar chart placeholder component
 */

import React from 'react';
import { Card, Empty } from 'antd';

const BarChart = ({ data, title, timeRange, setTimeRange }) => {
  return (
    <Card className="bar-chart">
      <div className="text-center py-8">
        <Empty 
          description="Chart functionality will be implemented with a charting library"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
        {title && <div className="mt-2 text-gray-500">{title}</div>}
      </div>
    </Card>
  );
};

export default BarChart;
