/**
 * Interviewer Store - STANDARDIZED VERSION
 *
 * Responsibilities:
 * - Assessment management and reviews
 * - Candidate evaluation tracking
 * - Earnings tracking and analytics
 * - Interviewer-specific workflows
 * - Performance metrics
 * - Calendar and scheduling (non-interview)
 *
 * Follows standardized store patterns:
 * - Consistent naming conventions
 * - Standardized loading/error states
 * - Unified cache management
 * - Common action patterns
 *
 * Does NOT handle:
 * - Authentication (handled by auth store)
 * - Profile data (handled by auth store)
 * - Interview video calls (removed)
 * - Interview creation (removed)
 */

import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import { supabase } from '@/utils/supabaseClient';

// Initial state following standardized pattern
const initialInterviewerState = {
  // === CORE DATA STATE ===
  // Assessment requests state
  assessmentRequests: [],

  // Completed assessments state
  completedAssessments: [],

  // Candidate evaluations state
  candidateEvaluations: [],

  // Earnings state
  earnings: null,
  earningsHistory: [],

  // Performance metrics
  performanceMetrics: null,

  // Selected assessment for detailed view
  selectedAssessment: null,

  // === STANDARDIZED UI STATE ===
  // Main loading/error states
  loading: false,
  error: null,

  // Specific loading states for complex operations
  assessmentsLoading: false,
  evaluationsLoading: false,
  earningsLoading: false,
  metricsLoading: false,

  // Specific error states for better error handling
  assessmentsError: null,
  evaluationsError: null,
  earningsError: null,
  metricsError: null,

  // === STANDARDIZED CACHE MANAGEMENT ===
  _cache: {
    lastAssessmentsFetch: null,
    lastEvaluationsFetch: null,
    lastEarningsFetch: null,
    lastMetricsFetch: null,
    assessmentsExpiry: 5 * 60 * 1000, // 5 minutes
    evaluationsExpiry: 10 * 60 * 1000, // 10 minutes
    earningsExpiry: 15 * 60 * 1000, // 15 minutes
    metricsExpiry: 30 * 60 * 1000, // 30 minutes
    assessmentsById: new Map(),
  },
};

const useInterviewerStore = create(
  devtools(
    persist(
      (set, get) => ({
        ...initialInterviewerState,

        // === STANDARDIZED UI STATE HELPERS ===
        setLoading: (loading) => set({ loading }, false, 'interviewer:setLoading'),
        setError: (error) => set({ error }, false, 'interviewer:setError'),
        clearError: () => set({ error: null }, false, 'interviewer:clearError'),

        setAssessmentsLoading: (loading) =>
          set({ assessmentsLoading: loading }, false, 'interviewer:setAssessmentsLoading'),
        setAssessmentsError: (error) =>
          set({ assessmentsError: error }, false, 'interviewer:setAssessmentsError'),
        clearAssessmentsError: () =>
          set({ assessmentsError: null }, false, 'interviewer:clearAssessmentsError'),

        setEvaluationsLoading: (loading) =>
          set({ evaluationsLoading: loading }, false, 'interviewer:setEvaluationsLoading'),
        setEvaluationsError: (error) =>
          set({ evaluationsError: error }, false, 'interviewer:setEvaluationsError'),
        clearEvaluationsError: () =>
          set({ evaluationsError: null }, false, 'interviewer:clearEvaluationsError'),

        // === STANDARDIZED CACHE HELPERS ===
        updateAssessmentsCache: () => {
          const { _cache } = get();
          set(
            {
              _cache: { ..._cache, lastAssessmentsFetch: Date.now() },
            },
            false,
            'interviewer:updateAssessmentsCache'
          );
        },

        isAssessmentsCacheValid: () => {
          const { _cache } = get();
          return (
            _cache.lastAssessmentsFetch &&
            Date.now() - _cache.lastAssessmentsFetch < _cache.assessmentsExpiry
          );
        },

        updateEvaluationsCache: () => {
          const { _cache } = get();
          set(
            {
              _cache: { ..._cache, lastEvaluationsFetch: Date.now() },
            },
            false,
            'interviewer:updateEvaluationsCache'
          );
        },

        isEvaluationsCacheValid: () => {
          const { _cache } = get();
          return (
            _cache.lastEvaluationsFetch &&
            Date.now() - _cache.lastEvaluationsFetch < _cache.evaluationsExpiry
          );
        },

        // === ASSESSMENT MANAGEMENT ACTIONS ===
        fetchAssessmentRequests: async (interviewerId, forceRefresh = false) => {
          if (!forceRefresh && get().isAssessmentsCacheValid()) {
            return get().assessmentRequests;
          }

          get().setAssessmentsLoading(true);
          get().clearAssessmentsError();

          try {
            const { data, error } = await supabase
              .from('interviews')
              .select(
                `
                *,
                candidate_profiles:candidate_id (
                  id,
                  full_name,
                  email,
                  phone,
                  years_of_experience,
                  title,
                  location,
                  avatar_url
                ),
                jobs:job_id (
                  id,
                  title,
                  company_profiles:company_id (
                    company_name
                  )
                )
              `
              )
              .eq('interviewer_id', interviewerId)
              .eq('status', 'requested')
              .order('created_at', { ascending: false });

            if (error) throw error;

            set(
              { assessmentRequests: data || [] },
              false,
              'interviewer:fetchAssessmentRequests:success'
            );
            get().updateAssessmentsCache();

            return data || [];
          } catch (error) {
            console.error('Error fetching assessment requests:', error);
            get().setAssessmentsError(error.message);
            return [];
          } finally {
            get().setAssessmentsLoading(false);
          }
        },

        fetchCompletedAssessments: async (interviewerId, forceRefresh = false) => {
          if (!forceRefresh && get().isEvaluationsCacheValid()) {
            return get().completedAssessments;
          }

          get().setEvaluationsLoading(true);
          get().clearEvaluationsError();

          try {
            const { data, error } = await supabase
              .from('interviews')
              .select(
                `
                *,
                candidate_profiles:candidate_id (
                  id,
                  full_name,
                  email,
                  phone,
                  years_of_experience,
                  title,
                  location,
                  avatar_url
                ),
                jobs:job_id (
                  id,
                  title,
                  company_profiles:company_id (
                    company_name
                  )
                )
              `
              )
              .eq('interviewer_id', interviewerId)
              .eq('status', 'completed')
              .order('updated_at', { ascending: false });

            if (error) throw error;

            set(
              { completedAssessments: data || [] },
              false,
              'interviewer:fetchCompletedAssessments:success'
            );
            get().updateEvaluationsCache();

            return data || [];
          } catch (error) {
            console.error('Error fetching completed assessments:', error);
            get().setEvaluationsError(error.message);
            return [];
          } finally {
            get().setEvaluationsLoading(false);
          }
        },

        // === EARNINGS MANAGEMENT ===
        fetchEarnings: async (interviewerId) => {
          set({ earningsLoading: true, earningsError: null });

          try {
            const { data, error } = await supabase
              .from('interviewer_earnings')
              .select('*')
              .eq('interviewer_id', interviewerId)
              .order('created_at', { ascending: false });

            if (error) throw error;

            const totalEarnings = data?.reduce((sum, earning) => sum + earning.amount, 0) || 0;

            set({
              earningsHistory: data || [],
              earnings: { total: totalEarnings, history: data || [] },
            });

            return { total: totalEarnings, history: data || [] };
          } catch (error) {
            console.error('Error fetching earnings:', error);
            set({ earningsError: error.message });
            return { total: 0, history: [] };
          } finally {
            set({ earningsLoading: false });
          }
        },

        // === PERFORMANCE METRICS ===
        fetchPerformanceMetrics: async (interviewerId) => {
          set({ metricsLoading: true, metricsError: null });

          try {
            // Calculate metrics from completed interviews
            const { data: assessments, error } = await supabase
              .from('interviews')
              .select('*')
              .eq('interviewer_id', interviewerId)
              .eq('status', 'completed');

            if (error) throw error;

            const metrics = {
              totalAssessments: assessments?.length || 0,
              averageScore: 0,
              completionRate: 0,
              responseTime: 0,
            };

            if (assessments && assessments.length > 0) {
              const scores = assessments
                .map((a) => a.evaluations?.[0]?.overall_score)
                .filter((score) => score !== null && score !== undefined);

              metrics.averageScore =
                scores.length > 0
                  ? scores.reduce((sum, score) => sum + score, 0) / scores.length
                  : 0;
            }

            set({ performanceMetrics: metrics });
            return metrics;
          } catch (error) {
            console.error('Error fetching performance metrics:', error);
            set({ metricsError: error.message });
            return null;
          } finally {
            set({ metricsLoading: false });
          }
        },

        // === ASSESSMENT ACTIONS ===
        submitAssessmentEvaluation: async (assessmentId, evaluationData) => {
          set({ loading: true, error: null });

          try {
            // Create evaluation record
            const { data: evaluation, error: evalError } = await supabase
              .from('candidate_evaluations')
              .insert({
                assessment_id: assessmentId,
                ...evaluationData,
                created_at: new Date().toISOString(),
              })
              .select()
              .single();

            if (evalError) throw evalError;

            // Update interview status
            const { error: updateError } = await supabase
              .from('interviews')
              .update({
                status: 'completed',
                updated_at: new Date().toISOString(),
              })
              .eq('id', assessmentId);

            if (updateError) throw updateError;

            // Refresh data
            const state = get();
            const updatedRequests = state.assessmentRequests.filter(
              (req) => req.id !== assessmentId
            );
            set({ assessmentRequests: updatedRequests });

            return { success: true, data: evaluation };
          } catch (error) {
            console.error('Error submitting assessment evaluation:', error);
            set({ error: error.message });
            return { success: false, error: error.message };
          } finally {
            set({ loading: false });
          }
        },

        // === STANDARDIZED RESET METHOD ===
        resetStore: () => {
          set(initialInterviewerState, false, 'interviewer:resetStore');
        },
      }),
      {
        name: 'interviewer-storage',
        partialize: (state) => ({
          assessmentRequests: state.assessmentRequests,
          completedAssessments: state.completedAssessments,
          candidateEvaluations: state.candidateEvaluations,
          earnings: state.earnings,
          performanceMetrics: state.performanceMetrics,
          _cache: state._cache,
        }),
      }
    ),
    { name: 'interviewer-store' }
  )
);

export default useInterviewerStore;
