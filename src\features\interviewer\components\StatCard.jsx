/**
 * StatCard Component for Interviewer Dashboard
 * 
 * Displays key statistics in a card format
 */

import React from 'react';
import { Card, Statistic } from 'antd';

const StatCard = ({ icon, title, value, subtitle, color = '#1890ff' }) => {
  return (
    <Card className="stat-card">
      <div className="flex items-center">
        <div 
          className="flex items-center justify-center w-12 h-12 rounded-lg mr-4"
          style={{ backgroundColor: `${color}20`, color }}
        >
          {icon}
        </div>
        <div className="flex-1">
          <Statistic
            title={title}
            value={value}
            valueStyle={{ color, fontSize: '24px', fontWeight: 'bold' }}
          />
          {subtitle && (
            <div className="text-sm text-gray-500 mt-1">
              {subtitle}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default StatCard;
