{"name": "flyt", "private": true, "version": "1.0.6", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "npm run eslint && npm run stylelint", "eslint": "eslint . --cache", "stylelint": "stylelint '**/*.css' --cache", "prettier": "prettier --check \"**/*.{js,jsx}\"", "prettier:write": "prettier --write \"**/*.{js,jsx}\"", "format": "prettier --write \"**/*.{js,jsx,css,json,md}\"", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:storage": "node test-storage.js"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/plots": "^2.4.0", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/vite": "^4.1.5", "antd": "^5.24.9", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "emoji-mart": "^5.6.0", "framer-motion": "^12.10.1", "lucide-react": "^0.511.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.4.0", "react-toastify": "^11.0.5", "tailwindcss": "^4.1.5", "uuid": "^11.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.23.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@tailwindcss/postcss": "^4.1.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.23.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^13.24.0", "identity-obj-proxy": "^3.0.0", "jsdom": "^26.0.0", "prettier": "^3.5.3", "prop-types": "^15.8.1", "vite": "^6.2.2"}, "packageManager": "yarn@4.9.1+sha512.f95ce356460e05be48d66401c1ae64ef84d163dd689964962c6888a9810865e39097a5e9de748876c2e0bf89b232d583c33982773e9903ae7a76257270986538"}